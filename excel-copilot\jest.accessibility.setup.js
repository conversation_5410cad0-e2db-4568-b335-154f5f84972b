import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';
import { toHaveNoViolations } from 'jest-axe';

// Estender expect com jest-axe
expect.extend(toHaveNoViolations);

// Configurar testing-library
configure({
  testIdAttribute: 'data-testid',
  // Aumentar timeout para testes de acessibilidade
  asyncUtilTimeout: 5000,
});

// Mock do IntersectionObserver para testes
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock do ResizeObserver para testes
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock do matchMedia para testes de responsive design
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock do getComputedStyle para testes de CSS
Object.defineProperty(window, 'getComputedStyle', {
  value: () => ({
    getPropertyValue: () => '',
  }),
});

// Mock do localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock do sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock do performance.now para testes de timing
global.performance = {
  ...global.performance,
  now: jest.fn(() => Date.now()),
};

// Suprimir warnings específicos durante os testes
const originalError = console.error;
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is no longer supported') ||
       args[0].includes('Warning: An invalid form control'))
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

// Configuração global para testes de acessibilidade
beforeEach(() => {
  // Limpar mocks antes de cada teste
  jest.clearAllMocks();
  
  // Reset do localStorage/sessionStorage
  localStorageMock.getItem.mockClear();
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();
  
  sessionStorageMock.getItem.mockClear();
  sessionStorageMock.setItem.mockClear();
  sessionStorageMock.removeItem.mockClear();
  sessionStorageMock.clear.mockClear();
});

// Configuração para testes de tema
export const mockThemeProvider = {
  theme: 'light',
  setTheme: jest.fn(),
  themes: ['light', 'dark'],
  systemTheme: 'light',
  resolvedTheme: 'light',
};

// Helper para renderizar componentes com providers necessários
export const renderWithProviders = (ui, options = {}) => {
  const { render } = require('@testing-library/react');
  const { ThemeProvider } = require('next-themes');
  
  const AllTheProviders = ({ children }) => {
    return (
      <ThemeProvider
        attribute="class"
        defaultTheme="light"
        enableSystem={false}
        {...mockThemeProvider}
      >
        {children}
      </ThemeProvider>
    );
  };

  return render(ui, { wrapper: AllTheProviders, ...options });
};

// Helper para testes de acessibilidade com configuração customizada
export const axeConfig = {
  rules: {
    // Desabilitar regras que podem ser problemáticas em testes
    'color-contrast': { enabled: true },
    'landmark-one-main': { enabled: false },
    'page-has-heading-one': { enabled: false },
    'region': { enabled: false },
  },
  tags: ['wcag2a', 'wcag2aa', 'wcag21aa'],
};

// Mock para hooks customizados
jest.mock('@/hooks/useReducedMotion', () => ({
  useReducedMotion: jest.fn(() => false),
}));

jest.mock('@/hooks/useTheme', () => ({
  useTheme: jest.fn(() => mockThemeProvider),
}));
