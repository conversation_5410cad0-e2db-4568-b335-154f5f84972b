const nextJest = require('next/jest');

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
});

// Add any custom config to be passed to Jest
const customJestConfig = {
  displayName: 'Accessibility Tests',
  setupFilesAfterEnv: ['<rootDir>/jest.accessibility.setup.js'],
  testMatch: [
    '<rootDir>/src/__tests__/accessibility/**/*.test.{js,jsx,ts,tsx}',
    '<rootDir>/src/__tests__/accessibility/**/*.spec.{js,jsx,ts,tsx}',
  ],
  testEnvironment: 'jsdom',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/components/ui/**/*.{js,jsx,ts,tsx}',
    'src/components/theme-provider.tsx',
    '!src/components/ui/index.ts',
    '!**/*.d.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  testTimeout: 10000,
  verbose: true,
  // Configurações específicas para testes de acessibilidade
  globals: {
    'ts-jest': {
      tsconfig: {
        jsx: 'react-jsx',
      },
    },
  },
  // Ignorar warnings específicos do jest-axe
  filterConsole: (log) => {
    // Filtrar warnings conhecidos do jest-axe
    if (log.includes('jest-axe')) return false;
    return true;
  },
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig);
