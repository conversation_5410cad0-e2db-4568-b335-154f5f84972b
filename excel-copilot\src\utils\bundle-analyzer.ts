import React from 'react';

/**
 * Utilitários para análise e otimização de bundle
 */

interface BundleMetrics {
  componentName: string;
  size: number;
  loadTime: number;
  isLazy: boolean;
  dependencies: string[];
}

interface PerformanceMetrics {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  ttfb: number; // Time to First Byte
}

class BundleAnalyzer {
  private metrics: BundleMetrics[] = [];
  private performanceMetrics: Partial<PerformanceMetrics> = {};

  /**
   * Registra métricas de um componente
   */
  recordComponent(metrics: BundleMetrics) {
    this.metrics.push(metrics);
    
    // Log apenas em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log(`📦 Component: ${metrics.componentName}`, {
        size: `${(metrics.size / 1024).toFixed(2)}KB`,
        loadTime: `${metrics.loadTime.toFixed(2)}ms`,
        isLazy: metrics.isLazy,
        dependencies: metrics.dependencies.length,
      });
    }
  }

  /**
   * Analisa performance de Web Vitals
   */
  recordWebVitals(metric: { name: string; value: number }) {
    switch (metric.name) {
      case 'FCP':
        this.performanceMetrics.fcp = metric.value;
        break;
      case 'LCP':
        this.performanceMetrics.lcp = metric.value;
        break;
      case 'FID':
        this.performanceMetrics.fid = metric.value;
        break;
      case 'CLS':
        this.performanceMetrics.cls = metric.value;
        break;
      case 'TTFB':
        this.performanceMetrics.ttfb = metric.value;
        break;
    }

    // Alertar sobre métricas ruins
    this.checkPerformanceThresholds(metric);
  }

  /**
   * Verifica se as métricas estão dentro dos limites aceitáveis
   */
  private checkPerformanceThresholds(metric: { name: string; value: number }) {
    const thresholds = {
      FCP: 1800, // 1.8s
      LCP: 2500, // 2.5s
      FID: 100,  // 100ms
      CLS: 0.1,  // 0.1
      TTFB: 800, // 800ms
    };

    const threshold = thresholds[metric.name as keyof typeof thresholds];
    if (threshold && metric.value > threshold) {
      console.warn(`⚠️ Performance Warning: ${metric.name} (${metric.value}) exceeds threshold (${threshold})`);
    }
  }

  /**
   * Identifica componentes que podem ser otimizados
   */
  getOptimizationSuggestions() {
    const suggestions: string[] = [];

    // Componentes grandes que não são lazy
    const largeNonLazyComponents = this.metrics.filter(
      m => m.size > 50 * 1024 && !m.isLazy // > 50KB
    );

    if (largeNonLazyComponents.length > 0) {
      suggestions.push(
        `Consider lazy loading these large components: ${largeNonLazyComponents.map(c => c.componentName).join(', ')}`
      );
    }

    // Componentes com muitas dependências
    const heavyDependencyComponents = this.metrics.filter(
      m => m.dependencies.length > 10
    );

    if (heavyDependencyComponents.length > 0) {
      suggestions.push(
        `These components have many dependencies and could be split: ${heavyDependencyComponents.map(c => c.componentName).join(', ')}`
      );
    }

    // Componentes com tempo de carregamento lento
    const slowComponents = this.metrics.filter(
      m => m.loadTime > 100 // > 100ms
    );

    if (slowComponents.length > 0) {
      suggestions.push(
        `These components load slowly: ${slowComponents.map(c => c.componentName).join(', ')}`
      );
    }

    return suggestions;
  }

  /**
   * Gera relatório completo de performance
   */
  generateReport() {
    const totalSize = this.metrics.reduce((sum, m) => sum + m.size, 0);
    const lazyComponents = this.metrics.filter(m => m.isLazy).length;
    const suggestions = this.getOptimizationSuggestions();

    return {
      summary: {
        totalComponents: this.metrics.length,
        totalSize: `${(totalSize / 1024).toFixed(2)}KB`,
        lazyComponents,
        lazyPercentage: `${((lazyComponents / this.metrics.length) * 100).toFixed(1)}%`,
      },
      webVitals: this.performanceMetrics,
      components: this.metrics.sort((a, b) => b.size - a.size),
      suggestions,
    };
  }

  /**
   * Limpa métricas coletadas
   */
  reset() {
    this.metrics = [];
    this.performanceMetrics = {};
  }
}

// Instância singleton
export const bundleAnalyzer = new BundleAnalyzer();

/**
 * HOC para medir performance de componentes
 */
export function withBundleAnalysis<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  return function AnalyzedComponent(props: P) {
    const name = componentName || Component.displayName || Component.name || 'UnknownComponent';
    const startTime = performance.now();

    React.useEffect(() => {
      const loadTime = performance.now() - startTime;
      
      // Estimar tamanho do componente (aproximação)
      const estimatedSize = name.length * 100; // Estimativa básica
      
      bundleAnalyzer.recordComponent({
        componentName: name,
        size: estimatedSize,
        loadTime,
        isLazy: false, // Será atualizado se for lazy
        dependencies: [], // Seria preenchido com análise estática
      });
    }, []);

    return React.createElement(Component, props);
  };
}

/**
 * Hook para medir performance de componentes
 */
export function useBundleAnalysis(componentName: string) {
  const startTime = React.useRef(performance.now());

  React.useEffect(() => {
    return () => {
      const loadTime = performance.now() - startTime.current;
      
      bundleAnalyzer.recordComponent({
        componentName,
        size: 0, // Seria calculado com ferramentas de build
        loadTime,
        isLazy: false,
        dependencies: [],
      });
    };
  }, [componentName]);

  return {
    recordMetric: (metricName: string, value: number) => {
      bundleAnalyzer.recordWebVitals({ name: metricName, value });
    },
  };
}

/**
 * Utilitário para detectar componentes não utilizados
 */
export function detectUnusedComponents() {
  if (typeof window === 'undefined') return [];

  const usedComponents = new Set<string>();
  
  // Analisar DOM para componentes renderizados
  const elements = document.querySelectorAll('[data-component]');
  elements.forEach(el => {
    const componentName = el.getAttribute('data-component');
    if (componentName) {
      usedComponents.add(componentName);
    }
  });

  return Array.from(usedComponents);
}

/**
 * Configuração para análise automática em desenvolvimento
 */
export function setupBundleAnalysis() {
  if (process.env.NODE_ENV !== 'development') return;

  // Configurar Web Vitals
  if (typeof window !== 'undefined') {
    // Simular coleta de Web Vitals (seria integrado com biblioteca real)
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'largest-contentful-paint') {
          bundleAnalyzer.recordWebVitals({ name: 'LCP', value: entry.startTime });
        }
      });
    });

    try {
      observer.observe({ type: 'largest-contentful-paint', buffered: true });
    } catch (e) {
      console.warn('Performance Observer not supported');
    }

    // Relatório automático a cada 30 segundos em desenvolvimento
    setInterval(() => {
      const report = bundleAnalyzer.generateReport();
      if (report.components.length > 0) {
        console.group('📊 Bundle Analysis Report');
        console.table(report.summary);
        if (report.suggestions.length > 0) {
          console.warn('💡 Optimization Suggestions:', report.suggestions);
        }
        console.groupEnd();
      }
    }, 30000);
  }
}

// Auto-setup em desenvolvimento
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  setupBundleAnalysis();
}
