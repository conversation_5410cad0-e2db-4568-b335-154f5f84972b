'use client';

import { Suspense, lazy, ComponentType, ReactNode, useState, useEffect } from 'react';

import { Skeleton } from './skeleton';
import { ErrorMessage } from './error-message';

interface LazyComponentProps {
  /** Função que retorna a Promise do componente */
  loader: () => Promise<{ default: ComponentType<any> }>;
  /** Componente de fallback durante o carregamento */
  fallback?: ReactNode;
  /** Componente de erro personalizado */
  errorFallback?: ComponentType<{ error: Error; resetErrorBoundary: () => void }>;
  /** Props a serem passadas para o componente carregado */
  componentProps?: Record<string, any>;
  /** Altura estimada para o skeleton (em pixels) */
  skeletonHeight?: number;
  /** Largura estimada para o skeleton */
  skeletonWidth?: string;
  /** Número de linhas do skeleton */
  skeletonLines?: number;
}

/**
 * Componente de fallback padrão com skeleton
 */
function DefaultFallback({ height = 200, width = '100%', lines = 3 }: {
  height?: number;
  width?: string;
  lines?: number;
}) {
  return (
    <div className="space-y-3 animate-pulse" style={{ height, width }}>
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton key={i} className="h-4 w-full" />
      ))}
    </div>
  );
}

/**
 * Componente de erro padrão
 */
function DefaultErrorFallback({ error, resetErrorBoundary }: {
  error: Error;
  resetErrorBoundary: () => void;
}) {
  return (
    <div className="p-4 border border-destructive/50 rounded-lg bg-destructive/10">
      <h3 className="text-sm font-medium text-destructive mb-2">
        Erro ao carregar componente
      </h3>
      <p className="text-xs text-muted-foreground mb-3">
        {error.message}
      </p>
      <button
        onClick={resetErrorBoundary}
        className="text-xs px-3 py-1 bg-destructive text-destructive-foreground rounded hover:bg-destructive/90"
      >
        Tentar novamente
      </button>
    </div>
  );
}

/**
 * Componente para lazy loading otimizado com error boundary e fallbacks
 */
export function LazyComponent({
  loader,
  fallback,
  errorFallback = DefaultErrorFallback,
  componentProps = {},
  skeletonHeight = 200,
  skeletonWidth = '100%',
  skeletonLines = 3,
}: LazyComponentProps) {
  // Criar componente lazy
  const LazyLoadedComponent = lazy(loader);

  // Fallback padrão se não fornecido
  const defaultFallback = fallback || (
    <DefaultFallback 
      height={skeletonHeight} 
      width={skeletonWidth} 
      lines={skeletonLines} 
    />
  );

  return (
    <div className="lazy-component-wrapper">
      <Suspense fallback={defaultFallback}>
        <LazyLoadedComponent {...componentProps} />
      </Suspense>
    </div>
  );
}

/**
 * HOC para criar componentes lazy com configuração pré-definida
 */
export function createLazyComponent<T extends ComponentType<any>>(
  loader: () => Promise<{ default: T }>,
  options: Omit<LazyComponentProps, 'loader' | 'componentProps'> = {}
) {
  return function LazyWrapper(props: React.ComponentProps<T>) {
    return (
      <LazyComponent
        loader={loader}
        componentProps={props}
        {...options}
      />
    );
  };
}

/**
 * Componentes lazy pré-configurados para componentes pesados comuns
 */

// Lazy loading para SpreadsheetEditor
export const LazySpreadsheetEditor = createLazyComponent(
  () => import('@/components/workbook/SpreadsheetEditor').then(module => ({ default: module })),
  {
    skeletonHeight: 400,
    skeletonLines: 8,
    fallback: (
      <div className="w-full h-96 bg-muted/20 rounded-lg flex items-center justify-center">
        <div className="text-center space-y-2">
          <Skeleton className="h-8 w-48 mx-auto" />
          <Skeleton className="h-4 w-32 mx-auto" />
        </div>
      </div>
    ),
  }
);

// Lazy loading para componentes de exemplo (podem ser removidos se não existirem)
export const LazyExampleComponent = createLazyComponent(
  () => Promise.resolve({ default: () => <div>Componente de exemplo</div> }),
  {
    skeletonHeight: 300,
    skeletonLines: 6,
    fallback: (
      <div className="w-full h-72 bg-muted/20 rounded-lg p-4">
        <div className="space-y-3">
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-10 w-full" />
        </div>
      </div>
    ),
  }
);

/**
 * Hook para lazy loading condicional baseado em viewport
 */
export function useLazyLoading(threshold = 0.1) {
  const [isVisible, setIsVisible] = useState(false);
  const [ref, setRef] = useState<Element | null>(null);

  useEffect(() => {
    if (!ref) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry && entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold }
    );

    observer.observe(ref);

    return () => observer.disconnect();
  }, [ref, threshold]);

  return { ref: setRef, isVisible };
}
