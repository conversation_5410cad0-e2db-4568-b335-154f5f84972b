import { render, screen } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import userEvent from '@testing-library/user-event';

// Estender expect com jest-axe
expect.extend(toHaveNoViolations);

// Importar componentes UI para teste
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { ErrorMessage } from '@/components/ui/error-message';
import { MotionSafe } from '@/components/ui/motion-safe';

// Mock do hook useReducedMotion para testes
jest.mock('@/hooks/useReducedMotion', () => ({
  useReducedMotion: jest.fn(() => false),
}));

describe('Accessibility Tests', () => {
  describe('Button Component', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <Button>Test Button</Button>
      );
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have proper ARIA attributes when disabled', async () => {
      render(<Button disabled>Disabled Button</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-disabled', 'true');
    });

    it('should be keyboard accessible', async () => {
      const user = userEvent.setup();
      const handleClick = jest.fn();
      
      render(<Button onClick={handleClick}>Clickable Button</Button>);
      const button = screen.getByRole('button');
      
      await user.tab();
      expect(button).toHaveFocus();
      
      await user.keyboard('{Enter}');
      expect(handleClick).toHaveBeenCalledTimes(1);
      
      await user.keyboard(' ');
      expect(handleClick).toHaveBeenCalledTimes(2);
    });
  });

  describe('Input Component', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <div>
          <label htmlFor="test-input">Test Label</label>
          <Input id="test-input" placeholder="Enter text" />
        </div>
      );
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have proper label association', () => {
      render(
        <div>
          <label htmlFor="labeled-input">Email Address</label>
          <Input id="labeled-input" type="email" />
        </div>
      );
      
      const input = screen.getByLabelText('Email Address');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('type', 'email');
    });

    it('should support ARIA descriptions', () => {
      render(
        <div>
          <label htmlFor="described-input">Password</label>
          <Input 
            id="described-input" 
            type="password" 
            aria-describedby="password-help"
          />
          <div id="password-help">Must be at least 8 characters</div>
        </div>
      );
      
      const input = screen.getByLabelText('Password');
      expect(input).toHaveAttribute('aria-describedby', 'password-help');
    });
  });

  describe('Textarea Component', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <div>
          <label htmlFor="test-textarea">Comments</label>
          <Textarea id="test-textarea" placeholder="Enter your comments" />
        </div>
      );
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <div>
          <label htmlFor="nav-textarea">Description</label>
          <Textarea id="nav-textarea" />
        </div>
      );
      
      const textarea = screen.getByLabelText('Description');
      await user.tab();
      expect(textarea).toHaveFocus();
    });
  });

  describe('ThemeToggle Component', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(<ThemeToggle />);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have proper ARIA attributes', () => {
      render(<ThemeToggle />);
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label');
    });
  });

  describe('ErrorMessage Component', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <ErrorMessage 
          type="error" 
          message="Test error message" 
          description="This is a test error"
        />
      );
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have proper role for screen readers', () => {
      render(
        <ErrorMessage 
          type="error" 
          message="Error occurred" 
        />
      );
      
      const alert = screen.getByRole('alert');
      expect(alert).toBeInTheDocument();
    });
  });

  describe('MotionSafe Component', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <MotionSafe>
          <div>Animated content</div>
        </MotionSafe>
      );
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should respect reduced motion preferences', () => {
      const { useReducedMotion } = require('@/hooks/useReducedMotion');
      useReducedMotion.mockReturnValue(true);
      
      render(
        <MotionSafe fallback={<div>Static content</div>}>
          <div>Animated content</div>
        </MotionSafe>
      );
      
      expect(screen.getByText('Static content')).toBeInTheDocument();
      expect(screen.queryByText('Animated content')).not.toBeInTheDocument();
    });
  });

  describe('Focus Management', () => {
    it('should maintain logical tab order', async () => {
      const user = userEvent.setup();
      
      render(
        <div>
          <Button>First</Button>
          <Input placeholder="Second" />
          <Button>Third</Button>
        </div>
      );
      
      const firstButton = screen.getByText('First');
      const input = screen.getByPlaceholderText('Second');
      const thirdButton = screen.getByText('Third');
      
      await user.tab();
      expect(firstButton).toHaveFocus();
      
      await user.tab();
      expect(input).toHaveFocus();
      
      await user.tab();
      expect(thirdButton).toHaveFocus();
    });

    it('should have visible focus indicators', () => {
      render(<Button>Focus Test</Button>);
      const button = screen.getByRole('button');
      
      // Verificar se o botão tem classes de focus
      expect(button).toHaveClass('focus:outline-none', 'focus:ring-2');
    });
  });

  describe('Color Contrast', () => {
    it('should have sufficient color contrast for text', async () => {
      const { container } = render(
        <div className="text-foreground bg-background p-4">
          <h1>High Contrast Heading</h1>
          <p>This text should have sufficient contrast.</p>
        </div>
      );
      
      const results = await axe(container, {
        rules: {
          'color-contrast': { enabled: true },
        },
      });
      
      expect(results).toHaveNoViolations();
    });
  });

  describe('Screen Reader Support', () => {
    it('should provide meaningful text for screen readers', () => {
      render(
        <div>
          <button aria-label="Close dialog">×</button>
          <img src="/test.jpg" alt="Test image description" />
          <span className="sr-only">Hidden text for screen readers</span>
        </div>
      );
      
      expect(screen.getByLabelText('Close dialog')).toBeInTheDocument();
      expect(screen.getByAltText('Test image description')).toBeInTheDocument();
      expect(screen.getByText('Hidden text for screen readers')).toBeInTheDocument();
    });
  });
});
